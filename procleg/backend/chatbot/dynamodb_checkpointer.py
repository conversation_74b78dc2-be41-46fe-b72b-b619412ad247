
import json
import os
import time
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, Optional, Sequence, Tuple, NamedTuple, Union

from dotenv import load_dotenv
from procleg.backend.services.conversation_thread_services import ConversationThreadModel
from procleg.backend.services import s3_services
from procleg.logger_config import get_logger
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage

# Import necessary types from langgraph
try:
    from langgraph.checkpoint.base import CheckpointTuple, CheckpointMetadata, Checkpoint
    from langgraph.checkpoint.serde.jsonplus import JsonPlusSerializer
    # ChannelVersions is typically a type alias for Dict[str, Union[int, float, str]]
    try:
        from langgraph.checkpoint.base import ChannelVersions
    except ImportError:
        # Define fallback type if ChannelVersions import fails
        ChannelVersions = Dict[str, Union[int, float, str]]
except ImportError:
    # Define fallback types if imports fail
    class CheckpointMetadata(Dict[str, Any]):  # type: ignore
        pass


    class Checkpoint(Dict[str, Any]):  # type: ignore
        pass


    class CheckpointTuple(NamedTuple):  # type: ignore
        config: RunnableConfig
        checkpoint: Checkpoint
        metadata: CheckpointMetadata
        parent_config: Optional[RunnableConfig] = None
        pending_writes: Optional[list] = None

    # Define fallback type for ChannelVersions
    ChannelVersions = Dict[str, Union[int, float, str]]

    # Define fallback JsonPlusSerializer
    class JsonPlusSerializer:
        def dumps(self, obj):
            # Simple fallback serialization that returns a string
            def default_serializer(o):
                if hasattr(o, '__dict__'):
                    return o.__dict__
                elif hasattr(o, '__class__'):
                    # Handle special objects like messages
                    return str(o)
                return str(o)
            return json.dumps(obj, default=default_serializer, ensure_ascii=False)

        def loads(self, data):
            if isinstance(data, bytes):
                data = data.decode('utf-8')
            return json.loads(data)

logger = get_logger(__name__)
load_dotenv()

LANGGRAPH_MODULE = "langgraph_state"
LANGGRAPH_WRITES_MODULE = "langgraph_writes"  # Keep if used, though not directly related to this fix

ERROR = "error"
SCHEDULED = "scheduled"
INTERRUPT = "interrupt"
RESUME = "resume"
WRITES_IDX_MAP = {ERROR: -1, SCHEDULED: -2, INTERRUPT: -3, RESUME: -4}  # Keep if used


# Custom serializer for DynamoDB string storage
class DynamoDBSerializer:
    """Custom serializer optimized for DynamoDB string storage."""

    def dumps(self, obj):
        """Serialize object to JSON string."""
        def default_serializer(o):
            # Handle LangChain message objects
            if isinstance(o, (HumanMessage, AIMessage, SystemMessage)):
                return {
                    "type": o.__class__.__name__.lower().replace("message", ""),
                    "content": o.content,
                    "additional_kwargs": o.additional_kwargs,
                    "name": getattr(o, 'name', None)
                }
            # Handle objects with __dict__
            elif hasattr(o, '__dict__'):
                return o.__dict__
            # Handle other objects
            else:
                return str(o)

        return json.dumps(obj, default=default_serializer, ensure_ascii=False)

    def loads(self, data):
        """Deserialize JSON string to object."""
        if isinstance(data, bytes):
            data = data.decode('utf-8')
        return json.loads(data)


class DynamoDBCheckpointer:
    def __init__(self, max_retries=3, retry_delay=0.1, prid="system"):
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.prid = prid
        # Use our custom serializer optimized for DynamoDB string storage
        self.serde = DynamoDBSerializer()
        logger.info("DynamoDB checkpointer initialized using ConversationThreadModel")

    def get_tuple(self, config: RunnableConfig) -> Optional[CheckpointTuple]:
        thread_id = config.get("configurable", {}).get("thread_id", "")
        if not thread_id:
            logger.warning("No thread_id found in config, cannot retrieve checkpoint tuple")
            return None

        try:
            thread = ConversationThreadModel.get_thread_by_primary_key(thread_id, LANGGRAPH_MODULE)

            if thread is None or thread.state_data is None:
                logger.debug(f"No checkpoint found in DynamoDB for thread_id: {thread_id}")
                return None  # LangGraph will initialize a new checkpoint for this config

            saved_data = self.serde.loads(thread.state_data)
            checkpoint_dict = saved_data.get("checkpoint")
            retrieved_metadata_dict = saved_data.get("metadata")
            retrieved_new_versions = saved_data.get("new_versions", {})

            if not checkpoint_dict:
                logger.error(
                    f"Corrupted data: 'checkpoint' field missing in saved state for thread_id: {thread_id}")
                return None

            current_checkpoint: Checkpoint = checkpoint_dict

            # Construct CheckpointMetadata object
            # Default to values LangGraph expects for a new/initial state if metadata is missing/incomplete
            current_metadata = CheckpointMetadata(
                source="loop",  # A common source, can be updated if retrieved_metadata_dict has it
                step=-1,  # Crucial: default for pre-first-step or if missing
                writes={},
                score=None,
                source_ts=datetime.now(timezone.utc).isoformat()
            )

            if retrieved_metadata_dict:
                current_metadata.update(
                    retrieved_metadata_dict)  # Load all fields from saved metadata
                if "step" not in retrieved_metadata_dict:
                    # If metadata was saved but 'step' is missing (e.g. old format)
                    logger.warning(
                        f"'step' key missing in retrieved metadata for thread_id: {thread_id}. Defaulting step to -1.")
                    current_metadata["step"] = -1
            else:
                logger.warning(
                    f"No metadata found alongside checkpoint for thread_id: {thread_id}. Initializing with default step -1.")

            return CheckpointTuple(
                config=config,
                checkpoint=current_checkpoint,
                metadata=current_metadata,
                parent_config=None,
                pending_writes=None
            )
        except json.JSONDecodeError:
            logger.error(f"Failed to decode state_data for thread_id: {thread_id}. Returning None.",
                         exc_info=True)
            return None
        except Exception as e:
            logger.error(f"Error retrieving checkpoint tuple for thread_id {thread_id}: {e}",
                         exc_info=True)
            return None

    def get_next_version(self, current, channel):
        if isinstance(current, str):
            raise NotImplementedError("String versions not supported")
        elif current is None:
            return 1
        else:
            return current + 1

    # get(self, key: str) is not part of the standard BaseCheckpointSaver interface used by get_tuple.
    # get_tuple should directly fetch and process. If you have a separate get method, ensure it's
    # aligned with what get_tuple needs or refactor get_tuple to incorporate its logic.

    def put(
        self,
        config: RunnableConfig,
        checkpoint: Checkpoint,  # langgraph.checkpoint.base.Checkpoint (which is a TypedDict)
        metadata: CheckpointMetadata,
        # langgraph.checkpoint.base.CheckpointMetadata (also TypedDict)
        new_versions: ChannelVersions,  # New channel versions as of this write
    ) -> RunnableConfig:  # Standard put returns RunnableConfig or Coroutine[RunnableConfig]
        thread_id = config.get("configurable", {}).get("thread_id", "")
        if not thread_id:
            logger.warning("No thread_id found in config, cannot store checkpoint")
            return config

        # Data to be stored in DynamoDB, including the metadata which contains the 'step'
        data_to_store = {
            "checkpoint": checkpoint,
            "metadata": metadata,
            "new_versions": new_versions
        }

        try:
            state_json = self.serde.dumps(data_to_store)
        except Exception as e:
            logger.error(f"Error serializing data_to_store for thread_id {thread_id}: {e}",
                         exc_info=True)
            raise  # Re-raise to indicate failure

        retries = 0
        thread_name = f"LangGraph State {thread_id}"

        while retries < self.max_retries:
            try:
                thread = ConversationThreadModel.get_thread_by_primary_key(thread_id,
                                                                           LANGGRAPH_MODULE)

                if thread:
                    current_db_version = thread.version or 1  # For optimistic locking of the DB record
                    new_db_version = current_db_version + 1

                    actions = [
                        ConversationThreadModel.state_data.set(state_json),
                        ConversationThreadModel.version.set(new_db_version),
                        ConversationThreadModel.updatedAt.set(datetime.now(timezone.utc))
                    ]
                    # PynamoDB's update raises a ConditionCheckFailedError if condition isn't met
                    thread.update(actions=actions,
                                  condition=(ConversationThreadModel.version == current_db_version))
                    logger.debug(
                        f"Updated state for thread_id: {thread_id} (db_version {current_db_version} -> {new_db_version})")
                    return config
                else:
                    ConversationThreadModel.create_thread(
                        thread_id=thread_id,
                        module=LANGGRAPH_MODULE,
                        prid=self.prid,
                        text_s3_url="",  # Not used for this type of state
                        last_modified_by="langgraph",
                        thread_name=thread_name,
                        state_data=state_json,
                        version=1  # Initial DB record version
                    )
                    logger.debug(f"Created new state for thread_id: {thread_id}")
                    return config
            except Exception as e:  # Should be more specific, e.g., pynamodb.exceptions.UpdateError for condition failures
                retries += 1
                if retries >= self.max_retries:
                    logger.error(
                        f"Failed to save state for thread_id {thread_id} after {self.max_retries} attempts: {e}",
                        exc_info=True)
                    raise
                logger.warning(
                    f"Concurrency conflict or error saving state for thread_id {thread_id}, retrying ({retries}/{self.max_retries}): {e}")
                time.sleep(self.retry_delay * (2 ** retries))  # Exponential backoff
        # This line should ideally not be reached if an error is raised after max_retries
        return config

    def put_writes(
        self,
        config: RunnableConfig,
        writes: Sequence[Tuple[str, Any]],
        task_id: str,
        # task_path: str = "", # task_path is not a standard arg for BaseCheckpointSaver.put_writes
    ) -> None:  # Standard put_writes returns None or Coroutine[None]
        thread_id = config.get("configurable", {}).get("thread_id", "")
        if not thread_id:
            logger.warning("No thread_id found in config, cannot store writes for task_id %s",
                           task_id)
            return

        module_for_writes = f"{LANGGRAPH_WRITES_MODULE}_{task_id}"  # Unique module per task

        writes_data_content = {}
        for i, (key_tuple_str, value) in enumerate(
            writes):  # Assuming writes are (str_representation_of_key_tuple, value)
            # Using a simple list of writes, as internal structure of writes can be complex
            # Storing them as a list [{ "key": "...", "value": ...}] might be better
            writes_data_content[f"write_{i}"] = {"key": key_tuple_str, "value": value}

        writes_json = self.serde.dumps(writes_data_content)

        try:
            thread = ConversationThreadModel.get_thread_by_primary_key(thread_id, module_for_writes)
            if thread:
                thread.update_thread(
                    state_data=writes_json,  # Overwriting previous writes for this task_id
                    last_modified_by="langgraph_writes",
                    thread_name=f"LangGraph Writes {thread_id}-{task_id}"
                )
                logger.debug(f"Updated writes for thread: {thread_id}, task: {task_id}")
            else:
                ConversationThreadModel.create_thread(
                    thread_id=thread_id,
                    module=module_for_writes,
                    prid=self.prid,  # Or derive from config if available/necessary
                    text_s3_url="",  # Not applicable
                    last_modified_by="langgraph_writes",
                    thread_name=f"LangGraph Writes {thread_id}-{task_id}",
                    state_data=writes_json,
                    version=1
                )
                logger.debug(f"Created new writes entry for thread: {thread_id}, task: {task_id}")
        except Exception as e:
            logger.error(f"Error storing writes for thread {thread_id}, task {task_id}: {e}",
                         exc_info=True)

    # async def aput_writes is also part of the interface, if you need async support.
    # For simplicity, it can call the sync version if true async DB operations aren't used.
    async def aput(
        self,
        config: RunnableConfig,
        checkpoint: Checkpoint,
        metadata: CheckpointMetadata,
        new_versions: ChannelVersions,
    ) -> RunnableConfig:
        return self.put(config, checkpoint, metadata, new_versions)

    async def aget_tuple(self, config: RunnableConfig) -> Optional[CheckpointTuple]:
        return self.get_tuple(config)

    async def aput_writes(
        self,
        config: RunnableConfig,
        writes: Sequence[Tuple[str, Any]],
        task_id: str,
    ) -> None:
        return self.put_writes(config, writes, task_id)
# import json
# import os
# import time
# import uuid
# from datetime import datetime, timezone
# from typing import Any, Dict, Optional, Sequence, Tuple, NamedTuple
#
# from dotenv import load_dotenv
# from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
# from langchain_core.runnables import RunnableConfig
# from pynamodb.exceptions import UpdateError, \
#     ConditionalCheckFailedException  # For specific DDB error handling
#
# from procleg.backend.services import s3_services
# from procleg.backend.services.conversation_thread_services import ConversationThreadModel
# from procleg.logger_config import get_logger
#
# # Configuration for S3 offloading (Ideally, move to environment variables or a config file)
# S3_BUCKET_NAME_FOR_CHECKPOINTS = os.getenv('S3_BUCKET_NAME', 'aig-azcdi-us-alxn-procleg-ds-dev')
# S3_CHECKPOINT_OFFLOAD_PREFIX = "langgraph_checkpoints_offload/"  # S3 "folder" for these offloaded files
#
# # Threshold in bytes for channel_values JSON string size before offloading
# # DynamoDB limit is 400KB for the entire item. Let's set threshold for channel_values lower.
# CHANNEL_VALUES_SIZE_THRESHOLD_BYTES = int(
#     os.getenv('CHANNEL_VALUES_SIZE_THRESHOLD_BYTES', 250 * 1024))  # 250KB default
# # Import necessary types from langgraph
# try:
#     from langgraph.checkpoint.base import CheckpointTuple, CheckpointMetadata, Checkpoint
# except ImportError:
#     # Define fallback types if imports fail
#     class CheckpointMetadata(Dict[str, Any]):
#         pass
#
#
#     class Checkpoint(Dict[str, Any]):
#         pass
#
#
#     class CheckpointTuple(NamedTuple):
#         config: RunnableConfig
#         checkpoint: Checkpoint
#         metadata: CheckpointMetadata
#         parent_config: Optional[RunnableConfig] = None
#         pending_writes: Optional[list] = None
#
# logger = get_logger(__name__)
# load_dotenv()
#
# # Module name used to identify LangGraph state entries in the conversation thread table
# LANGGRAPH_MODULE = "langgraph_state"
# LANGGRAPH_WRITES_MODULE = "langgraph_writes"
#
# # Special write types
# ERROR = "error"
# SCHEDULED = "scheduled"
# INTERRUPT = "interrupt"
# RESUME = "resume"
# WRITES_IDX_MAP = {ERROR: -1, SCHEDULED: -2, INTERRUPT: -3, RESUME: -4}
#
#
# class LangChainMessageEncoder(json.JSONEncoder):
#     """Custom JSON encoder that can handle LangChain message objects."""
#
#     def default(self, obj):
#         if isinstance(obj, (HumanMessage, AIMessage, SystemMessage)):
#             # Convert message objects to a serializable dictionary
#             return {
#                 "type": obj.__class__.__name__,
#                 "content": obj.content,
#                 "additional_kwargs": obj.additional_kwargs
#             }
#         # Let the base class handle other types
#         return super().default(obj)
#
#
# class DynamoDBCheckpointer:
#     """A LangGraph checkpointer that uses ConversationThreadModel for state persistence with concurrency control."""
#
#     def __init__(self, max_retries=3, retry_delay=0.1, prid="system"):
#         """Initialize the DynamoDB checkpointer.
#
#         Args:
#             max_retries: Maximum number of retries for optimistic concurrency control
#             retry_delay: Delay between retries in seconds
#             prid: Project ID to use for the conversation threads (default: "system")
#         """
#         self.max_retries = max_retries
#         self.retry_delay = retry_delay
#         self.prid = prid
#         logger.info("DynamoDB checkpointer initialized using ConversationThreadModel")
#
#     # def get_tuple(self, config: RunnableConfig) -> Optional[CheckpointTuple]:
#     #     """Fetch a checkpoint tuple using the given configuration.
#     #
#     #     Args:
#     #         config (RunnableConfig): Configuration specifying which checkpoint to retrieve.
#     #
#     #     Returns:
#     #         Optional[CheckpointTuple]: The requested checkpoint tuple, or None if not found.
#     #     """
#     #     try:
#     #         # Extract thread_id from config
#     #         thread_id = config.get("configurable", {}).get("thread_id", "")
#     #         if not thread_id:
#     #             logger.warning("No thread_id found in config, cannot retrieve checkpoint")
#     #             return None
#     #
#     #         # Get the checkpoint data using the existing get method
#     #         checkpoint_data = self.get(thread_id)
#     #         if not checkpoint_data:
#     #             logger.debug(f"No checkpoint found for thread_id: {thread_id}")
#     #             return None
#     #
#     #         # Create a Checkpoint object
#     #         checkpoint = Checkpoint(
#     #             v=checkpoint_data.get("v", 1),
#     #             id=checkpoint_data.get("id", thread_id),
#     #             ts=checkpoint_data.get("ts", datetime.now(timezone.utc).isoformat()),
#     #             channel_values=checkpoint_data.get("channel_values", {}),
#     #             channel_versions=checkpoint_data.get("channel_versions", {}),
#     #             versions_seen=checkpoint_data.get("versions_seen", {}),
#     #             pending_sends=checkpoint_data.get("pending_sends", [])
#     #         )
#     #
#     #         # Create minimal metadata
#     #         metadata = CheckpointMetadata()
#     #
#     #         # Return the checkpoint tuple
#     #         return CheckpointTuple(
#     #             config=config,
#     #             checkpoint=checkpoint,
#     #             metadata=metadata,
#     #             parent_config=None,
#     #             pending_writes=None
#     #         )
#     #     except Exception as e:
#     #         logger.error(f"Error retrieving checkpoint tuple: {e}")
#     #         return None
#     #
#     #     # In class DynamoDBCheckpointer:
#     def get_tuple(self, config: RunnableConfig) -> Optional[CheckpointTuple]:
#             thread_id = config.get("configurable", {}).get("thread_id", "")
#             if not thread_id:
#                 logger.warning("No thread_id found in config, cannot retrieve checkpoint tuple")
#                 return None
#
#             try:
#                 thread = ConversationThreadModel.get_thread_by_primary_key(thread_id,
#                                                                            LANGGRAPH_MODULE)
#
#                 if thread is None or thread.state_data is None:
#                     logger.debug(f"No checkpoint found in DynamoDB for thread_id: {thread_id}")
#                     return None
#
#                 saved_data = json.loads(thread.state_data)
#                 checkpoint_dict = saved_data.get("checkpoint")
#                 retrieved_metadata_dict = saved_data.get("metadata")
#
#                 if not checkpoint_dict:
#                     logger.error(
#                         f"Corrupted data: 'checkpoint' field missing in saved state for thread_id: {thread_id}")
#                     return None
#
#                 # Potentially restore channel_values from S3
#                 if isinstance(checkpoint_dict.get('channel_values'), dict) and \
#                     checkpoint_dict['channel_values'].get("__s3_ref__") is True:
#
#                     s3_ref_info = checkpoint_dict['channel_values']
#                     s3_key_to_load = s3_ref_info.get("key")
#
#                     if not s3_key_to_load:
#                         logger.error(
#                             f"S3 reference found for thread {thread_id} but 'key' is missing. Cannot restore channel_values.")
#                         checkpoint_dict['channel_values'] = {}  # Fallback: empty or error
#                     else:
#                         logger.info(
#                             f"Restoring channel_values for thread {thread_id} from S3 key: {s3_key_to_load}")
#                         try:
#                             s3_data_bytes = s3_services.read_from_s3_file(s3_key_to_load,
#                                                                           S3_BUCKET_NAME_FOR_CHECKPOINTS)
#                             if s3_data_bytes:
#                                 actual_channel_values = json.loads(s3_data_bytes.decode('utf-8'))
#                                 checkpoint_dict['channel_values'] = actual_channel_values
#                                 logger.info(
#                                     f"Successfully restored channel_values from S3 for thread {thread_id}")
#                             else:
#                                 logger.error(
#                                     f"Failed to read channel_values from S3 key {s3_key_to_load} for thread {thread_id}. File empty or read error.")
#                                 checkpoint_dict['channel_values'] = {}  # Fallback
#                         except Exception as e:
#                             logger.error(
#                                 f"Error restoring channel_values from S3 for thread {thread_id}, key {s3_key_to_load}: {e}",
#                                 exc_info=True)
#                             checkpoint_dict['channel_values'] = {}  # Fallback
#
#                 current_checkpoint: Checkpoint = checkpoint_dict
#
#                 current_metadata = CheckpointMetadata(
#                     source="loop", step=-1, writes={}, score=None,
#                     source_ts=datetime.now(timezone.utc).isoformat()
#                 )
#                 if retrieved_metadata_dict:
#                     current_metadata.update(retrieved_metadata_dict)
#                     if "step" not in retrieved_metadata_dict:
#                         logger.warning(
#                             f"'step' key missing in retrieved metadata for thread_id: {thread_id}. Defaulting step to -1.")
#                         current_metadata["step"] = -1
#                 else:
#                     logger.warning(
#                         f"No metadata found for thread_id: {thread_id}. Initializing with default step -1.")
#
#                 return CheckpointTuple(
#                     config=config,
#                     checkpoint=current_checkpoint,
#                     metadata=current_metadata,
#                     parent_config=None,
#                     pending_writes=None
#                 )
#             except json.JSONDecodeError:
#                 logger.error(
#                     f"Failed to decode state_data for thread_id: {thread_id}. Returning None.",
#                     exc_info=True)
#                 return None
#             except Exception as e:
#                 logger.error(f"Error retrieving checkpoint tuple for thread_id {thread_id}: {e}",
#                              exc_info=True)
#                 return None
#
#     def get_next_version(self, current, channel):
#         """Generate the next version ID for a channel.
#
#         Args:
#             current: The current version identifier (int, float, or str)
#             channel: The channel being versioned
#
#         Returns:
#             The next version identifier, which must be increasing
#         """
#         if isinstance(current, str):
#             raise NotImplementedError("String versions not supported")
#         elif current is None:
#             return 1
#         else:
#             return current + 1
#
#     def get(self, key: str) -> Optional[Dict[str, Any]]:
#         """Get state by key.
#
#         Args:
#             key: The key to retrieve state for (used as thread_id)
#
#         Returns:
#             The state dictionary or None if not found
#         """
#         try:
#             logger.debug(f"Getting state for key: {key}")
#             thread = ConversationThreadModel.get_thread_by_primary_key(key, LANGGRAPH_MODULE)
#
#             if thread is None or thread.state_data is None:
#                 logger.debug(f"No state found for key: {key}")
#                 return None
#
#             state = json.loads(thread.state_data)
#             logger.debug(f"Retrieved state for key: {key}")
#             return state
#         except Exception as e:
#             logger.error(f"Error retrieving state for key {key}: {e}")
#             return None
#
#     def put(
#             self,
#             config: RunnableConfig,
#             checkpoint: Checkpoint,  # langgraph.checkpoint.base.Checkpoint (TypedDict)
#             metadata: CheckpointMetadata,
#             # langgraph.checkpoint.base.CheckpointMetadata (TypedDict)
#         ) -> RunnableConfig:
#             thread_id = config.get("configurable", {}).get("thread_id", "")
#             if not thread_id:
#                 logger.warning("No thread_id found in config, cannot store checkpoint")
#                 return config
#
#             # Make a shallow copy of the checkpoint to potentially modify channel_values for storage
#             checkpoint_to_store = checkpoint.copy()
#
#             # Check and offload channel_values if too large
#             if 'channel_values' in checkpoint_to_store and checkpoint_to_store['channel_values']:
#                 try:
#                     # Serialize to JSON to get an accurate byte size
#                     channel_values_json = json.dumps(checkpoint_to_store['channel_values'],
#                                                      cls=LangChainMessageEncoder)
#                     channel_values_bytes = channel_values_json.encode('utf-8')
#                     channel_values_size_actual_bytes = len(channel_values_bytes)
#
#                     if channel_values_size_actual_bytes > CHANNEL_VALUES_SIZE_THRESHOLD_BYTES:
#                         logger.info(
#                             f"Channel_values for thread {thread_id}, checkpoint {checkpoint.get('id')}, (size: {channel_values_size_actual_bytes}B) exceeds threshold {CHANNEL_VALUES_SIZE_THRESHOLD_BYTES}B. Offloading to S3.")
#
#                         checkpoint_uuid_str = str(checkpoint.get('id',
#                                                                  uuid.uuid4().hex))  # Use checkpoint ID or a new UUID
#
#                         s3_key = f"{S3_CHECKPOINT_OFFLOAD_PREFIX}{thread_id}/{checkpoint_uuid_str}/channel_values.json"
#
#                         # s3_services.upload_dict_to_s3 expects a dict
#                         # Pass the original dict, not the JSON string
#                         uploaded_s3_key = s3_services.upload_dict_to_s3(
#                             data_dict=checkpoint_to_store['channel_values'],
#                             bucket_name=S3_BUCKET_NAME_FOR_CHECKPOINTS,
#                             file_name=s3_key  # This is the S3 object key
#                         )
#
#                         if uploaded_s3_key:  # upload_dict_to_s3 returns the S3 key (file_name) on success
#                             logger.info(
#                                 f"Successfully offloaded channel_values for thread {thread_id}, checkpoint {checkpoint_uuid_str} to S3 key: {s3_key}")
#                             checkpoint_to_store['channel_values'] = {
#                                 "__s3_ref__": True,
#                                 "key": s3_key,
#                                 "original_type": type(checkpoint['channel_values']).__name__
#                             }
#                         else:
#                             logger.error(
#                                 f"Failed to offload channel_values to S3 for thread {thread_id}, checkpoint {checkpoint_uuid_str}. Storing inline, which may exceed DynamoDB limits.")
#                             # Fallback: checkpoint_to_store['channel_values'] remains unchanged, will attempt to store inline.
#                 except Exception as e:
#                     logger.error(
#                         f"Error during channel_values size check or S3 offload for thread {thread_id}, checkpoint {checkpoint.get('id')}: {e}",
#                         exc_info=True)
#                     # Proceed with original (potentially large) channel_values; DDB put might fail.
#
#             data_to_store = {
#                 "checkpoint": checkpoint_to_store,  # Use the potentially modified checkpoint
#                 "metadata": metadata
#             }
#
#             try:
#                 state_json = json.dumps(data_to_store, cls=LangChainMessageEncoder)
#                 # Double check total size of state_json before writing to DynamoDB
#                 total_state_size_bytes = len(state_json.encode('utf-8'))
#                 if total_state_size_bytes > 390 * 1024:  # Leave some buffer for DDB overhead
#                     logger.warning(
#                         f"Total state_data size for thread {thread_id} ({total_state_size_bytes}B) is close to or exceeds DynamoDB limit even after potential offload. DDB put may fail.")
#
#             except Exception as e:
#                 logger.error(f"Error serializing data_to_store for thread_id {thread_id}: {e}",
#                              exc_info=True)
#                 raise  # Re-raise to indicate failure
#
#             retries = 0
#             thread_name = f"LangGraph State {thread_id}"
#
#             while retries < self.max_retries:
#                 try:
#                     thread = ConversationThreadModel.get_thread_by_primary_key(thread_id,
#                                                                                LANGGRAPH_MODULE)
#
#                     if thread:
#                         current_db_version = thread.version or 1
#                         new_db_version = current_db_version + 1
#
#                         actions = [
#                             ConversationThreadModel.state_data.set(state_json),
#                             ConversationThreadModel.version.set(new_db_version),
#                             ConversationThreadModel.updatedAt.set(datetime.now(timezone.utc))
#                         ]
#                         thread.update(actions=actions, condition=(
#                             ConversationThreadModel.version == current_db_version))
#                         logger.debug(
#                             f"Updated state for thread_id: {thread_id} (db_version {current_db_version} -> {new_db_version})")
#                         return config
#                     else:
#                         ConversationThreadModel.create_thread(
#                             thread_id=thread_id,
#                             module=LANGGRAPH_MODULE,
#                             prid=self.prid,
#                             text_s3_url="",
#                             last_modified_by="langgraph",
#                             thread_name=thread_name,
#                             state_data=state_json,
#                             version=1
#                         )
#                         logger.debug(f"Created new state for thread_id: {thread_id}")
#                         return config
#                 except (ConditionalCheckFailedException,
#                         UpdateError) as e:  # More specific exception for optimistic lock failure
#                     retries += 1
#                     if retries >= self.max_retries:
#                         logger.error(
#                             f"Failed to save state for thread_id {thread_id} after {self.max_retries} attempts (conditional check failed): {e}",
#                             exc_info=True)
#                         raise
#                     logger.warning(
#                         f"Optimistic locking failed for thread_id {thread_id}, retrying ({retries}/{self.max_retries}): {e}")
#                     time.sleep(self.retry_delay * (2 ** retries))  # Exponential backoff
#                 except Exception as e:  # Catch other unexpected errors
#                     logger.error(f"Unexpected error saving state for thread_id {thread_id}: {e}",
#                                  exc_info=True)
#                     raise  # Re-raise other critical errors immediately
#             return config
#     def put_writes(
#         self,
#         config: RunnableConfig,
#         writes: Sequence[Tuple[str, Any]],
#         task_id: str,
#         task_path: str = "",
#     ) -> None:
#         """Store intermediate writes linked to a checkpoint.
#
#         Args:
#             config (RunnableConfig): Configuration of the related checkpoint.
#             writes (List[Tuple[str, Any]]): List of writes to store.
#             task_id (str): Identifier for the task creating the writes.
#             task_path (str): Path of the task creating the writes.
#         """
#         try:
#             # Extract thread_id from config
#             thread_id = config.get("configurable", {}).get("thread_id", "")
#             if not thread_id:
#                 logger.warning("No thread_id found in config, cannot store writes")
#                 return
#
#             # Create a unique module name for this task's writes
#             module = f"{LANGGRAPH_WRITES_MODULE}_{task_id}"
#
#             # Prepare writes data
#             writes_data = {}
#             for i, (write_type, write_value) in enumerate(writes):
#                 # Use special index for special write types
#                 idx = WRITES_IDX_MAP.get(write_type, i)
#                 writes_data[str(idx)] = {
#                     "type": write_type,
#                     "value": write_value,
#                     "task_path": task_path
#                 }
#
#             # Convert to JSON with custom encoder for LangChain message objects
#             writes_json = json.dumps(writes_data, cls=LangChainMessageEncoder)
#
#             # Try to get existing thread for this task
#             thread = ConversationThreadModel.get_thread_by_primary_key(thread_id, module)
#
#             if thread:
#                 # Update existing thread
#                 thread.update_thread(
#                     state_data=writes_json,
#                     last_modified_by="langgraph",
#                     thread_name=f"LangGraph Writes {task_id}"
#                 )
#                 logger.debug(f"Updated writes for thread: {thread_id}, task: {task_id}")
#             else:
#                 # Create new thread for this task's writes
#                 ConversationThreadModel.create_thread(
#                     thread_id=thread_id,
#                     module=module,
#                     prid=self.prid,
#                     text_s3_url="",  # Not used for writes
#                     last_modified_by="langgraph",
#                     thread_name=f"LangGraph Writes {task_id}",
#                     state_data=writes_json,
#                     version=1
#                 )
#                 logger.debug(f"Created new writes for thread: {thread_id}, task: {task_id}")
#
#         except Exception as e:
#             logger.error(f"Error storing writes for task {task_id}: {e}")
#
#     def delete(self, key: str) -> None:
#         """Delete state by key.
#
#         Args:
#             key: The key to delete state for (used as thread_id)
#         """
#         try:
#             logger.debug(f"Deleting state for key: {key}")
#             success = ConversationThreadModel.delete_thread_by_primary_key(key, LANGGRAPH_MODULE)
#             if success:
#                 logger.debug(f"Deleted state for key: {key}")
#             else:
#                 logger.debug(f"No state found to delete for key: {key}")
#         except Exception as e:
#             logger.error(f"Error deleting state for key {key}: {e}")
#
#     async def aput_writes(
#         self,
#         config: RunnableConfig,
#         writes: Sequence[Tuple[str, Any]],
#         task_id: str,
#         task_path: str = "",
#     ) -> None:
#         """Asynchronously store intermediate writes linked to a checkpoint.
#
#         This is a simple wrapper around the synchronous put_writes method since
#         our implementation doesn't support true async operations.
#
#         Args:
#             config (RunnableConfig): Configuration of the related checkpoint.
#             writes (List[Tuple[str, Any]]): List of writes to store.
#             task_id (str): Identifier for the task creating the writes.
#             task_path (str): Path of the task creating the writes.
#         """
#         # Just call the synchronous version since we don't have async DB access
#         self.put_writes(config, writes, task_id, task_path)
